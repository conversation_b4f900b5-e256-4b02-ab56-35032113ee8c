/* Import Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.css';

.headingh1{
  color: #006C8E;
text-align: center;
-webkit-text-stroke-width: 0.5px;
-webkit-text-stroke-color: #E0F8FF;
font-family: var(--font-poppins);
font-size: 48px;
font-weight: 900;
}
.subheadingh1{
 color: #004C63;
text-align: center;
font-family: var(--font-poppins);
font-size: 24px;
font-style: normal;
font-weight: 600;
}
.iris-minds .container-block .block-img img {
    width: 300px
  }
  .iris-minds .player-container {
    margin-top: -180px;
    padding: 25px;
  }
  .iris-minds .player-container .plyr {
    border-radius: 15px;
    border: 3px solid #fff;
  }
  .iris-minds .container-block {
    align-items: center;
    justify-content: center;
  }
  .iris-minds .hero iframe {
    position: absolute;
  }
  
  .iris-minds .h2 {
    font-weight: 700;
    font-size: 2.1rem;
    margin-top: 23px;
    color: #004C63;
  }

  .iris-minds .h1 {
   color: #006C8E;
    text-align: center;
    -webkit-text-stroke-width: 0.5px;
    -webkit-text-stroke-color: #E0F8FF;
    font-size: 3rem;
    font-style: normal;
    font-weight: bold;
    font-family: var(--font-poppins);
  }
  .iris-fiks-container{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 5rem;
    margin-bottom: 4rem;
  }
  .iris-fiks-container > div{
    padding: 0 3rem;
      width: 40%;
      align-items: center;
  }
  .iris-fiks-container-img img{
    width: 330px;
    height: auto;
  }
  .container-block p {
    font-weight: 500;
    font-size: 1.3rem;
  }
  .iris-minds .iris-ribbon-block {
    position: relative;
    margin-bottom: 350px;
    margin-top: 30px;
  }
  .iris-minds .iris-ribbon {
    position: absolute;
    background-image: url(../../../public/images/iris/iris-flyer.png);
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 225px;
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
  }
  .iris-minds .mt-7 {
    margin-top: 5rem;
  }
  .iris-minds .hero {
    height: 680px;
    width: 100%;
    background-image: url(../../../public/images/iris/iris-landing-header.webp);
    background-size: cover;  
    background-position: center center; 
  }
  .iris-content-box{
    background-image: url(../../../public/images/iris/iris-content-background.png);
    background-size: cover; 
  }
  .iris-minds .hero .header {
    padding-top: 1rem;
    position: relative;
  }
  .iris-minds .btn-primary,
  .btn-ghost {
    position: relative;
    background-color: #f66007;
    outline: none;
    padding: 15px 25px;
    border: 1px solid #fff;
    font-weight: 700;
    border-radius: 20px;
  }
  .iris-minds .btn-primary:hover,
  .btn-ghost:hover {
    border: 1px solid rgba(153, 153, 153, 0.753);
  }
  .btn-ghost {
    background-color: transparent;
    border: 1px solid #333;
    color: #333;
    margin-right: 15px;
    padding-right: 35px;
  }
  .btn-ghost img {
    position: absolute;
    right: 20px;
    top: 20px;
    width: 7px;
  }
  .iris-minds .certification-block {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  .iris-minds .certification {
    width: 242px;
    margin: 0 12px;
    text-align: center;
    margin-top: 20px;
  }

  .certification img{
    width: 150px;
    height: auto;
  }
  
  .games-heading{
  font-size: 2rem;
  font-weight: 700;
  text-align: center;
  }
  
  .iris-minds .games-block {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }
  .iris-minds .games {
    margin: 0 12px; 
    padding: 20px;
    margin-top: 20px;
    border: 1px solid #F2F2F2;
    border-radius: 4px;
  }
  
  .iris-minds .games img{
    display: block
  }
  
  .iris-minds .games a{
    text-decoration: none;
  }
  
  .iris-minds .games:hover{
    box-shadow: 0 4px 8px rgba(93, 93, 93, 0.3);
    transition: box-shadow 400ms ease-in-out;
  }
  
  .iris-minds .games span{
    font-weight: 700;
    display: inline-block;
    font-size: 20px;
    margin: 0.5rem 0;
    text-align: left !important;
  }
  
  .iris-minds .games button{
  background-color: #F66007;
  display: block;
  width: 100%;
  border-radius: 4px;
  height: 48px;
  font-size: 1.3rem;
  font-weight: 700;
  outline: none;
  border: none;
  color: #ffffff
  }
  .iris-minds .iris-carousel {
    background-color: #224154;
    border-radius: 20px;
  }
  .iris-minds .carousel-root {
    padding-top: 30px;
  }
  .iris-minds .iris-carousel .carousel .review-image {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .iris-minds .iris-carousel .carousel .review-image img{
   width: 400px
  }
  /* .iris-minds .iris-carousel .carousel .review-image .left {
    position: absolute;
    width: 230px;
    margin-left: -125px;
  }
  .iris-minds .iris-carousel .carousel .review-image .center {
    width: 280px;
    z-index: 2;
  }
  .iris-minds .iris-carousel .carousel .review-image .right {
    position: absolute;
    width: 230px;
    right: 60px;
  } */
  .iris-minds .iris-minds .right-block {
    text-align: left;
    padding: 0 25px;
    /* margin-top: 25px; */
  }
  .iris-minds .iris-carousel .carousel h2 {
    color: #fff;
  }
  .iris-minds .iris-carousel .carousel p {
    color: #fff;
    font-weight: 500;
    padding-right: 50px;
    line-height: 1.75rem;
  }
  .iris-minds .iris-carousel .arrow {
    display: inline-block;
    position: absolute;
    bottom: 0;
    right: 50%;
    cursor: pointer;
    z-index: 9;
  }
  .iris-minds .mr-50 {
    margin: 50px;
  }
  .iris-minds .iris-carousel .arrow.right-arrow {
    margin-right: -100px;
  }
  .iris-minds .iris-carousel .btn-primary {
    border-radius: 28px;
    border-color: #ffffff40;
    padding: 20px 25px;
  }
  .iris-minds .iris-carousel .btn-primary:hover {
    border-color: #ffffffa4;
  }
  /* .iris-minds .iris-carousel .dots {
    position: absolute;
    z-index: 9;
    bottom: 20px;
    display: flex;
    align-items: center;
  }
  .iris-minds .iris-carousel .dots span {
    background-color: #e9d2ff;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin: 5px;
  }
  .iris-minds .iris-carousel .dots span.active {
    background-color: rgb(140 73 193);
    width: 15px;
    height: 15px;
  } */
  .iris-fiks-container p{
    font-size: 1.2rem;
    font-weight: 600;
  }
  @media only screen and (max-width: 450px) {
    .iris-minds .iris-carousel .carousel .review-image img{
      width: 340px
    }
    .iris-minds{
      margin-top: 45px;
    }
    /* .iris-minds h2 */
    .iris-minds .h2 {
      font-size: 1.5rem;
      font-weight: 700;
      margin-top: 10px;
      color: #004C63;
    }
    .iris-fiks-container h2{
      font-size: 2rem;
      font-weight: 700;
    }
    .iris-minds .mt-7 {
      margin-top: 2rem;
    }
    .iris-minds .iris-ribbon-block {
      margin-bottom: 265px;
      margin-top: 20px;
    }
    .iris-minds .iris-ribbon {
      background-image: url(../../../public/images/iris/iris-flyer-mb.png);
    }
    .iris-minds .iris-carousel .carousel .review-image .right {
      right: 5px;
    }
    .iris-minds .iris-carousel .carousel .review-image {
      transform: scale(0.9);
      margin-top: -15px;
    }
    .iris-minds .carousel-root {
      padding-top: 25px;
    }
    .iris-minds .iris-minds .right-block {
      margin-top: -10px;
      padding-bottom: 70px;
    }
    .iris-minds .player-container {
      padding: 5px;
      margin-top: -90px;
    }
    .iris-content-box{
        background-image: url(../../../public/images/iris/iris-content-background-mb.png);
        /* background-position: center center; */
        background-size: auto;
        background-repeat: no-repeat;
      }
      .iris-minds .hero {
        background-image: url(../../../public/images/iris/iris-landing-header-mb.webp);
        background-position: center center;
        background-size: cover;
        height: 468px;
        /* margin-top: 50px; */
      }
      .iris-fiks-container{
        flex-direction: column;
        margin-bottom: 0rem;
      }
      .iris-fiks-container > div{
        padding: 0;
        width: 100%;
      }
      .iris-fiks-container-img{
        display: flex;
        justify-content: center;
        /* margin-bottom: 2rem; */
      }
  }
  /* iPad */
  @media all and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
    .iris-fiks-container > div {
      width: 70%
    }
    .iris-minds .carousel-root {
      padding-top: 05px;
    }
    .iris-minds .iris-carousel .carousel .review-image {
      transform: scale(0.7);
    }
    .iris-minds .iris-carousel .carousel .review-image .right {
      right: -40px;
    }
    .iris-minds .iris-carousel .carousel p {
      padding-right: 0;
    }
    .iris-minds .iris-minds .right-block {
      padding-bottom: 70px;
    }
  }
  @media all and (device-width: 820px) and (device-height: 1180px) and (orientation: portrait) {
    .iris-minds .carousel-root {
      padding-top: 05px;
    }
    .iris-minds .iris-carousel .carousel .review-image {
      transform: scale(0.7);
    }
    .iris-minds .iris-carousel .carousel .review-image .right {
      right: -40px;
    }
    .iris-minds .iris-carousel .carousel p {
      padding-right: 0;
    }
    .iris-minds .iris-minds .right-block {
      padding-bottom: 70px;
    }
  }

  @media all and (min-width: 768px) and (orientation: portrait) {
    .iris-fiks-container > div {
      width: 60%;
      padding: 0 1.5rem
    }
  }
  
  @media all and (min-width: 1180px) and (min-height: 820px) {
    .iris-minds .iris-minds .right-block {
      margin-top: -64px;
    }
    .iris-minds .iris-carousel .carousel .review-image img{
      width: 350px
    }
    .iris-minds .iris-carousel .btn-primary {
      padding: 15px 15px;

    }
  }
  @media all and (device-width: 1180px) and (device-height: 820px) and (orientation: landscape) {
    .iris-content-box {
      background-position: center;
    }
     .iris-minds .h2 {
    font-size: 1.7rem;
    margin-top: 59px;
    color: #004C63;
    }
  }
  
  /*video player*/
  :root {
    --plyr-control-icon-size: 24px;
    --plyr-color-main: #f76000;
    --plyr-range-fill-background: #00c6a3;
    --plyr-range-thumb-background: #f76000;
    --plyr-range-thumb-height: 20px;
    --plyr-range-thumb-shadow: 0 1px 1px rgba(215, 26, 18, 0.15),
      0 0 0 1px rgba(215, 26, 18, 0.2);
    --plyr-range-track-height: 08px;
  }
  
  .vjs-theme-skidos {
    --vjs-theme-skidos--primary: #00c6a3;
    --vjs-theme-skidos--secondary: #fff;
  }
  
  /* hide control */
  .plyr__controls .plyr__controls__item:first-child,
  .plyr--pip-supported [data-plyr="pip"],
  .plyr--fullscreen-enabled [data-plyr="fullscreen"] {
    display: none;
  }
  