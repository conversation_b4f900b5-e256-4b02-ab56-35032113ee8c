"use client";
import dynamic from "next/dynamic";
import Image from "next/image";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "./iris.css";

// Dynamically import Plyr to avoid SSR issues
const Plyr = dynamic(() => import("plyr-react"), {
  ssr: false,
  loading: () => <div>Loading video player...</div>,
});

import StoreIcons from "@/components/Footer/StoreIcons";
import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import "plyr-react/plyr.css";

const IrisPage = () => {
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen",
  ];
  const t = useTranslations("Footer");

  const router = useRouter();

  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    console.log("GA4 Event:", { category, action, label, nonInteraction });
  };

  const icons = [
    {
      src: "/images/footer/socialIcons/Amazon.webp",
      alt: t("Amazon"),
      onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
    },
    {
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: t("Appstore"),
      onClick: () =>
        window.open(
          "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: t("Playstore"),
      onClick: () =>
        window.open(
          "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Web.webp",
      alt: t("Web"),
      onClick: () => handleWebIcon(),
    },
  ];

  const { isSubscribed } = useAuth();

  const handleWebIcon = () => {
    trackWebEngageEvent("WebGLFooterIconClk");

    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/get-started");
    }
  };

  return (
    <>
      <div className="iris-minds">
        <div className="hero">
          <div className="container">
            <div className="row flex-column align-items-center d-flex justify-content-center header">
              <div className="col-12 col-md-5 text-center">
                <h1 className="headingh1">Learn to Read</h1>
                <h1 className="subheadingh1" style={{ margin: 0 }}>
                  An exciting journey to develop early reading skills
                </h1>

                <StoreIcons icons={icons} styleClass="mt-4" />
              </div>
            </div>
          </div>
        </div>

        <div className="iris-content-box">
          <div className="container" id="player-container">
            <div className="row d-flex justify-content-center">
              <div className="col-12 col-md-8">
                <div style={{ marginTop: "0rem" }} className="player-container">
                  <Plyr
                    id="plyr"
                    controls
                    options={{ volume: 0.1, controls }}
                    source={{
                      type: "video",
                      sources: [
                        {
                          src: "https://youtu.be/HAJHytYL1xA",
                          provider: "youtube",
                        },
                      ],
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="iris-fiks-container">
              <div className="iris-fiks-container-img">
                <Image
                  src="/images/iris/join-reading-adventure.png"
                  alt="Join Reading Adventure"
                  width={300}
                  height={200}
                />
              </div>
              <div>
                <h2>Join the reading adventure</h2>
                <p>Learn letters, phonics, and first words on a delightful treasure hunt!</p>
              </div>
            </div>

            <div className="iris-fiks-container">
              <div className="iris-fiks-container-img">
                <Image
                  src="/images/iris/science-of-reading.png"
                  alt="Science Of Reading"
                  width={300}
                  height={200}
                />
              </div>
              <div>
                <h2>Aligned with Science of Reading (SoR)</h2>
                <p>
                  Develop reading fluency and confidence with 20+ expert-approved learning
                  activities
                </p>
              </div>
            </div>
          </div>

          <div className="iris-ribbon-block">
            <div className="iris-ribbon"></div>
          </div>

          <div className="container iris-minds mt-7">
            <div className="row mt-3">
              <div className="col-12"></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default IrisPage;
