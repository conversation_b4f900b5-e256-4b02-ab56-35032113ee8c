"use client";

import StoreIcons from "@/components/Footer/StoreIcons";
import Image from "next/image";
import Plyr from "plyr-react";
import "plyr-react/plyr.css";
import ReactGA from "react-ga4";
import "react-responsive-carousel/lib/styles/carousel.min.css";
import "./rekindle.css";

import { useAuth } from "@/context/AuthContext";
import { trackWebEngageEvent } from "@/utils/webengage";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";

function RekindleMinds() {
  const controls = [
    "play-large",
    "play",
    "progress",
    "current-time",
    "mute",
    "captions",
    "pip",
    "airplay",
    "fullscreen",
  ];

  const t = useTranslations("Footer");
  const { isSubscribed } = useAuth();

  const router = useRouter();
  const eventTrackerGa4 = (category, action, label, nonInteraction) => {
    ReactGA.event({ category, action, label, nonInteraction });
  };

  const icons = [
    {
      src: "/images/footer/socialIcons/Amazon.webp",
      alt: t("Amazon"),
      onClick: () => window.open("https://www.amazon.com/gp/product/B0DH6RT2JV", "_blank"),
    },
    {
      src: "/images/footer/socialIcons/Appstore.webp",
      alt: t("Appstore"),
      onClick: () =>
        window.open(
          "https://apps.apple.com/us/app/skidos-learning-games-for-kids/id1483744837",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Playstore.webp",
      alt: t("Playstore"),
      onClick: () =>
        window.open(
          "https://play.google.com/store/apps/details?id=skidos.shopping.toddler.learning.games&hl=en_IN",
          "_blank"
        ),
    },
    {
      src: "/images/footer/socialIcons/Web.webp",
      alt: t("Web"),
      onClick: () => handleWebIcon(),
    },
  ];
  const handleWebIcon = () => {
    trackWebEngageEvent("WebGLFooterIconClk");

    if (isSubscribed) {
      router.push("/user-home-screen");
    } else {
      router.push("/get-started");
    }
  };

  return (
    <div className="rekindle-minds">
      <div className="hero">
        <div className="container">
          <div className="row d-flex justify-content-center header">
            <div className="col-12 col-md-8 text-center">
              <h1 className="headingh1">Emotional well-being</h1>
              <h1 className="subheadingh1" style={{ margin: 0 }}>
                A mindful game designed by teachers to boost your child’s emotional growth and
                learning
              </h1>

              <StoreIcons icons={icons} styleClass="mt-4" />
            </div>
          </div>
        </div>
      </div>

      <div className="container" id="player-container">
        <div className="row d-flex justify-content-center">
          <div className="col-12 col-md-8">
            <div className="player-container mt-2">
              <Plyr
                id="plyr"
                controls
                options={{ volume: 0.1, controls }}
                source={{
                  type: "video",
                  sources: [
                    {
                      src: "https://www.youtube.com/watch?v=yrgI2U9hAc8",
                      provider: "youtube",
                    },
                  ],
                }}
              />
            </div>
          </div>
        </div>

        <div className="row container-block mt-7">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image
              src="/images/rekindle-minds/why-play.png"
              alt="Why Play Rekindle Minds"
              width={500}
              height={300}
            />
          </div>
          <div className="col-12 col-md-6">
            <h2>Why play Rekindle Minds?</h2>
            <p>
              Rekindle Minds takes children on a playful deep-dive into their emotions. This
              delightful interactive game helps them build strong relationships, manage stress and
              develop a positive, confident personality.
            </p>
          </div>
        </div>

        <div className="row container-block mt-7">
          <div className="col-12 col-md-6">
            <h2>How does it work?</h2>
            <p>
              Explore, learn and grow with 150+ immersive stories, quizzes, puzzles, and games -
              designed by trusted educators to develop your child’s EQ
            </p>
          </div>
          <div className="col-12 col-md-6 block-img d-flex justify-content-end">
            <Image
              src="/images/rekindle-minds/how-it-works.png"
              alt="How it Works"
              width={500}
              height={300}
            />
          </div>
        </div>
      </div>

      <div className="ribbon-block">
        <div className="ribbon"></div>
      </div>

      <div className="container rekindle-minds mt-7" style={{ marginBottom: "4rem" }}>
        <div className="row container-block">
          <div className="col-12 col-md-6 block-img d-flex justify-content-start">
            <Image
              src="/images/rekindle-minds/progress-report.png"
              alt="Progress Report"
              width={500}
              height={300}
            />
          </div>
          <div className="col-12 col-md-6">
            <h2>Expert-approved guide to SEL</h2>
            <p>
              Lay a strong foundation for social-emotional learning with 5 playful islands designed
              as per the CASEL framework
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RekindleMinds;
