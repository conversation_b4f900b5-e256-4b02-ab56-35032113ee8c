/* Import Bootstrap CSS */
@import 'bootstrap/dist/css/bootstrap.css';

 .rekindle-minds .hero iframe {
    position: absolute;
  }

   .rekindle-minds .hero {
    height: 680px;
    width: 100%;
    background-image: url(../../../public/images/rekindle-minds/rekindle-landing.webp);
    background-size: cover;  
    background-position: center center; 
  }
.headingh1{
color: #006F5C;
text-align: center;
-webkit-text-stroke-width: 0.5px;
-webkit-text-stroke-color: #A0FFEE;
font-family: var(--font-poppins);
font-size: 48px;
font-weight: 900;
}
.subheadingh1{
color: #004E41;
text-align: center;
font-family: var(--font-poppins);
font-size: 24px;
font-weight: 600;
}

.rekindle-minds .container-block .block-img img {
  max-width: 100%;
  height: auto;
}
.rekindle-minds .player-container {
  margin-top: -150px;
  padding: 25px;
}
.rekindle-minds .player-container .plyr {
  border-radius: 15px;
  border: 1px solid #fff;
}
.rekindle-minds .container-block {
  align-items: center;
  justify-content: center;
}
.rekindle-minds .hero iframe {
  position: absolute;
}
.rekindle-minds h2,
.rekindle-minds .h2 {
  font-weight: 700;
  font-size: 1.75rem;
  margin-top: 15px;
}
.container-block p {
  font-weight: 500;
}
.rekindle-minds .ribbon-block {
  position: relative;
  margin-bottom: 350px;
  margin-top: 30px;
}
.rekindle-minds .ribbon {
  position: absolute;
  background-image: url(../../../public/images/rekindle-minds/ribbon.png);
  width: 100%;
  height: 225px;
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}
.rekindle-minds .mt-7 {
  margin-top: 5rem;
}
.rekindle-minds .hero {
  background-color: rgb(75 232 193);
  height: 680px;
  width: 100%;
}
.rekindle-minds .hero .header {
  padding-top: 2rem;
  position: relative;
}
.rekindle-minds .btn-primary,
.btn-ghost {
  position: relative;
  background-color: #f66007;
  outline: none;
  padding: 15px 25px;
  border: 1px solid #fff;
  font-weight: 700;
  border-radius: 20px;
}
.rekindle-minds .btn-primary:hover,
.btn-ghost:hover {
  border: 1px solid rgba(153, 153, 153, 0.753);
}
.btn-ghost {
  background-color: transparent;
  border: 1px solid #333;
  color: #333;
  margin-right: 15px;
  padding-right: 35px;
}
.btn-ghost img {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 7px;
}
.rekindle-minds .certification-block {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.rekindle-minds .certification {
  width: 242px;
  margin: 0 12px;
  text-align: center;
  margin-top: 20px;
}

.games-heading{
font-size: 2rem;
font-weight: 700;
text-align: center;
}

.rekindle-minds .games-block {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.rekindle-minds .games {
  margin: 0 12px; 
  padding: 20px;
  margin-top: 20px;
  border: 1px solid #F2F2F2;
  border-radius: 4px;
}

.rekindle-minds .games img{
  display: block
}

.rekindle-minds .games a{
  text-decoration: none;
}

.rekindle-minds .games:hover{
  box-shadow: 0 4px 8px rgba(93, 93, 93, 0.3);
  transition: box-shadow 400ms ease-in-out;
}

.rekindle-minds .games span{
  font-weight: 700;
  display: inline-block;
  font-size: 20px;
  margin: 0.5rem 0;
  text-align: left !important;
}

.rekindle-minds .games button{
background-color: #F66007;
display: block;
width: 100%;
border-radius: 4px;
height: 48px;
font-size: 1.3rem;
font-weight: 700;
outline: none;
border: none;
color: #ffffff
}
.rekindle-minds .rekindle-carousel {
  background-color: #b877f7;
  border-radius: 20px;
}
.rekindle-minds .carousel-root {
  padding-top: 50px;
}
.rekindle-minds .rekindle-carousel .carousel .review-image {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
.rekindle-minds .rekindle-carousel .carousel .review-image .left {
  position: absolute;
  width: 230px;
  margin-left: -125px;
  height: auto;
}
.rekindle-minds .rekindle-carousel .carousel .review-image .center {
  width: 280px;
  height: auto;
  z-index: 2;
}
.rekindle-minds .rekindle-carousel .carousel .review-image .right {
  position: absolute;
  height: auto;
  width: 230px;
  right: 60px;
}
.rekindle-minds .rekindle-minds .right-block {
  text-align: left;
  padding: 0 25px;
  margin-top: 25px;
}
.rekindle-minds .rekindle-carousel .carousel h2 {
  color: #fff;
}
.rekindle-minds .rekindle-carousel .carousel p {
  color: #fff;
  font-weight: 500;
  padding-right: 50px;
  line-height: 1.75rem;
}
.rekindle-minds .rekindle-carousel .arrow {
  display: inline-block;
  position: absolute;
  bottom: 0;
  right: 50%;
  cursor: pointer;
  z-index: 9;
}
.rekindle-minds .mr-50 {
  margin: 50px;
}
.rekindle-minds .rekindle-carousel .arrow.right-arrow {
  margin-right: -100px;
}
.rekindle-minds .rekindle-carousel .btn-primary {
  border-radius: 28px;
  border-color: #ffffff40;
  padding: 20px 25px;
}
.rekindle-minds .rekindle-carousel .btn-primary:hover {
  border-color: #ffffffa4;
}
.rekindle-minds .rekindle-carousel .dots {
  position: absolute;
  z-index: 9;
  bottom: 20px;
  display: flex;
  align-items: center;
}
.rekindle-minds .rekindle-carousel .dots span {
  background-color: #e9d2ff;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 5px;
}
.rekindle-minds .rekindle-carousel .dots span.active {
  background-color: rgb(140 73 193);
  width: 15px;
  height: 15px;
}
@media only screen and (max-width: 450px) {
  .rekindle-minds h2,
  .rekindle-minds .h2 {
    margin-top: 25px;
  }
  .rekindle-minds .mt-7 {
    margin-top: 2rem;
  }
  .rekindle-minds .ribbon-block {
    margin-bottom: 265px;
    margin-top: 20px;
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image .right {
    right: 5px;
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image {
    transform: scale(0.9);
    margin-top: -15px;
  }
  .rekindle-minds .carousel-root {
    padding-top: 25px;
  }
  .rekindle-minds .rekindle-minds .right-block {
    margin-top: -10px;
    padding-bottom: 70px;
  }
  .rekindle-minds .player-container {
    margin-top: -250px;
  }

  .rekindle-minds .hero {
        background-image: url(../../../public/images/rekindle-minds/rekindle-landing-mb.webp);
        background-position: center center;
        background-size: cover;
        height: 490px;
        /* margin-top: 50px; */
      }

      .headingh1{
font-size: 38px;
}
.subheadingh1{
font-size: 14px;
}
}
/* iPad */
@media all and (device-width: 768px) and (device-height: 1024px) and (orientation: portrait) {
  .rekindle-minds .carousel-root {
    padding-top: 05px;
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image {
    transform: scale(0.7);
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image .right {
    right: -40px;
  }
  .rekindle-minds .rekindle-carousel .carousel p {
    padding-right: 0;
  }
  .rekindle-minds .rekindle-minds .right-block {
    padding-bottom: 70px;
  }
}
@media all and (device-width: 820px) and (device-height: 1180px) and (orientation: portrait) {
  .rekindle-minds .carousel-root {
    padding-top: 05px;
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image {
    transform: scale(0.7);
  }
  .rekindle-minds .rekindle-carousel .carousel .review-image .right {
    right: -40px;
  }
  .rekindle-minds .rekindle-carousel .carousel p {
    padding-right: 0;
  }
  .rekindle-minds .rekindle-minds .right-block {
    padding-bottom: 70px;
  }
}
/* iPad Pro*/
@media all and (device-width: 1366px) and (device-height: 1024px) and (orientation: landscape) {
}

/*video player*/
:root {
  --plyr-control-icon-size: 24px;
  --plyr-color-main: #f76000;
  --plyr-range-fill-background: #00c6a3;
  --plyr-range-thumb-background: #f76000;
  --plyr-range-thumb-height: 20px;
  --plyr-range-thumb-shadow: 0 1px 1px rgba(215, 26, 18, 0.15),
    0 0 0 1px rgba(215, 26, 18, 0.2);
  --plyr-range-track-height: 08px;
}

.vjs-theme-skidos {
  --vjs-theme-skidos--primary: #00c6a3;
  --vjs-theme-skidos--secondary: #fff;
}

/* hide control */
.plyr__controls .plyr__controls__item:first-child,
.plyr--pip-supported [data-plyr="pip"],
.plyr--fullscreen-enabled [data-plyr="fullscreen"] {
  display: none;
}
